package cn.sdtbu.edu.controller;

import cn.sdtbu.edu.dto.ApiResponse;
import cn.sdtbu.edu.dto.UserProfileDTO;
import cn.sdtbu.edu.service.UserService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.web.servlet.MockMvc;

import java.sql.Timestamp;

import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * UserProfileController 根据邮箱获取用户信息功能测试类
 * <AUTHOR>
 */
@WebMvcTest(UserProfileController.class)
class UserProfileControllerEmailTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private UserService userService;

    @Autowired
    private ObjectMapper objectMapper;

    private UserProfileDTO testProfile;
    private String testEmail;
    private Integer testUserId;

    @BeforeEach
    void setUp() {
        testEmail = "<EMAIL>";
        testUserId = 1;
        
        testProfile = new UserProfileDTO();
        testProfile.setId(1);
        testProfile.setEmail(testEmail);
        testProfile.setUsername("testuser");
        testProfile.setAvatar("/img/avatar01.png");
        testProfile.setBio("测试用户简介");
        testProfile.setCreatedAt(new Timestamp(System.currentTimeMillis()));
        testProfile.setLikedProgramsCount(5);
        testProfile.setPlaylistsCount(2);
        testProfile.setCommentsCount(10);
    }

    @Test
    void testGetUserProfileByEmail_Success() throws Exception {
        when(userService.getUserProfileByEmail(testEmail)).thenReturn(testProfile);

        mockMvc.perform(get("/api/me/profile-by-email")
                .param("email", testEmail)
                .header("User-Id", testUserId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("根据邮箱获取用户信息成功"))
                .andExpect(jsonPath("$.data.id").value(testProfile.getId()))
                .andExpect(jsonPath("$.data.email").value(testProfile.getEmail()))
                .andExpect(jsonPath("$.data.username").value(testProfile.getUsername()))
                .andExpect(jsonPath("$.data.likedProgramsCount").value(testProfile.getLikedProgramsCount()));

        verify(userService).getUserProfileByEmail(testEmail);
    }

    @Test
    void testGetUserProfileByEmail_NoUserId() throws Exception {
        mockMvc.perform(get("/api/me/profile-by-email")
                .param("email", testEmail))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.code").value(400))
                .andExpect(jsonPath("$.message").value("用户未登录或用户ID无效"));

        verify(userService, never()).getUserProfileByEmail(anyString());
    }

    @Test
    void testGetUserProfileByEmail_InvalidUserId() throws Exception {
        mockMvc.perform(get("/api/me/profile-by-email")
                .param("email", testEmail)
                .header("User-Id", 0))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.code").value(400))
                .andExpect(jsonPath("$.message").value("用户未登录或用户ID无效"));

        verify(userService, never()).getUserProfileByEmail(anyString());
    }

    @Test
    void testGetUserProfileByEmail_EmptyEmail() throws Exception {
        mockMvc.perform(get("/api/me/profile-by-email")
                .param("email", "")
                .header("User-Id", testUserId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.code").value(400))
                .andExpect(jsonPath("$.message").value("邮箱地址不能为空"));

        verify(userService, never()).getUserProfileByEmail(anyString());
    }

    @Test
    void testGetUserProfileByEmail_EmailNotFound() throws Exception {
        when(userService.getUserProfileByEmail(testEmail))
                .thenThrow(new RuntimeException("邮箱地址不存在"));

        mockMvc.perform(get("/api/me/profile-by-email")
                .param("email", testEmail)
                .header("User-Id", testUserId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.code").value(404))
                .andExpect(jsonPath("$.message").value("邮箱地址不存在"));

        verify(userService).getUserProfileByEmail(testEmail);
    }

    @Test
    void testGetUserProfileByEmail_IllegalArgument() throws Exception {
        when(userService.getUserProfileByEmail(testEmail))
                .thenThrow(new IllegalArgumentException("邮箱格式不正确"));

        mockMvc.perform(get("/api/me/profile-by-email")
                .param("email", testEmail)
                .header("User-Id", testUserId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.code").value(400))
                .andExpect(jsonPath("$.message").value("邮箱格式不正确"));

        verify(userService).getUserProfileByEmail(testEmail);
    }
}
